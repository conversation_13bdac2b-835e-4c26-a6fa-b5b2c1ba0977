import Vision

/// Represents a single object detection result
struct DetectionResult: Identifiable, Equatable {
  let id = UUID()
  let boundingBox: CGRect
  let confidence: Float
  let label: String
  let timestamp: Date

  init(boundingBox: CGRect, confidence: Float, label: String) {
    self.boundingBox = boundingBox
    self.confidence = confidence
    self.label = label
    self.timestamp = Date()
  }

  /// Creates a DetectionResult from a VNRecognizedObjectObservation
  init(from observation: VNRecognizedObjectObservation) {
    self.boundingBox = observation.boundingBox
    self.confidence = observation.confidence

    // Get the top classification label
    if let topLabel = observation.labels.first {
      self.label = topLabel.identifier
    } else {
      self.label = "Unknown"
    }

    self.timestamp = Date()
  }

  /// Converts Vision coordinate system to UIKit coordinate system
  func convertedBoundingBox(for imageSize: CGSize) -> CGRect {
    let x = boundingBox.origin.x * imageSize.width
    let y = (1 - boundingBox.origin.y - boundingBox.height) * imageSize.height
    let width = boundingBox.width * imageSize.width
    let height = boundingBox.height * imageSize.height

    return CGRect(x: x, y: y, width: width, height: height)
  }

  /// Returns a formatted confidence percentage string
  var confidencePercentage: String {
    return confidence.formatted(.percent.precision(.fractionLength(1)))
  }

  /// Returns a display string combining label and confidence
  var displayText: String {
    return "\(label) (\(confidencePercentage))"
  }

  static func == (lhs: DetectionResult, rhs: DetectionResult) -> Bool {
    return lhs.id == rhs.id
  }
}

/// Represents the overall detection state for a frame
struct DetectionFrame {
  let detections: [DetectionResult]
  let timestamp: Date
  let frameSize: CGSize

  init(detections: [DetectionResult], frameSize: CGSize) {
    self.detections = detections
    self.frameSize = frameSize
    self.timestamp = Date()
  }

  /// Returns detections filtered by confidence threshold
  func filteredDetections(confidenceThreshold: Float) -> [DetectionResult] {
    return detections.filter { $0.confidence >= confidenceThreshold }
  }

  /// Returns the number of high-confidence detections
  func highConfidenceCount(threshold: Float = 0.7) -> Int {
    return detections.filter { $0.confidence >= threshold }.count
  }
}
