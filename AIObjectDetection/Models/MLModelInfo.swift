import Foundation

/// Represents information about a CoreML model
struct MLModelInfo: Identifiable, Codable, Equatable {
  let name: String
  let downloadURL: String
  let filePath: String?
  let fileSize: Int64

  var id: String { name }

  init(
    name: String,
    downloadURL: String,
    filePath: String? = nil,
    fileSize: Int64 = 0,
  ) {
    self.name = name
    self.filePath = filePath
    self.downloadURL = downloadURL
    self.fileSize = fileSize
  }

  /// Returns formatted file size string
  var formattedFileSize: String {
    return fileSize.formatted(
      .byteCount(style: .file, allowedUnits: [.kb, .mb, .gb], spellsOutZero: true))
  }

  /// Returns the model file URL if available
  var modelURL: URL? {
    guard let filePath = filePath else { return nil }
    return URL(fileURLWithPath: filePath)
  }

  func copy(
    downloadURL: String? = nil,
    filePath: String? = nil,
  ) -> MLModelInfo {
    return MLModelInfo(
      name: name,
      downloadURL: downloadURL ?? self.downloadURL,
      filePath: filePath ?? self.filePath,
      fileSize: fileSize
    )
  }

  static func == (lhs: MLModelInfo, rhs: MLModelInfo) -> Bool {
    return lhs.id == rhs.id
  }
}

extension MLModelInfo {
  static var mock: [MLModelInfo] {
    return (1...100).map {
      MLModelInfo(
        name: "Model \($0)",
        downloadURL: "",
        filePath: Bool.random() ? "/path/to/model\($0)" : nil,
        fileSize: 1024 * Int64.random(in: 10_000..<1_000_000),
      )
    }
  }
}
