import SwiftUI

/// Centralized app settings manager using @AppStorage
/// Provides a single source of truth for all app preferences
@Observable
class AppSettings {

  // MARK: - Singleton
  static let shared = AppSettings()

  // MARK: - Detection Settings
  var isDetectionEnabled: Bool {
    get { UserDefaults.standard.bool(forKey: Constants.AppStorageKeys.isDetectionEnabled) }
    set { UserDefaults.standard.set(newValue, forKey: Constants.AppStorageKeys.isDetectionEnabled) }
  }

  var confidenceThreshold: Double {
    get { UserDefaults.standard.double(forKey: Constants.AppStorageKeys.confidenceThreshold) }
    set {
      UserDefaults.standard.set(newValue, forKey: Constants.AppStorageKeys.confidenceThreshold)
    }
  }

  var showConfidenceScores: Bool {
    get { UserDefaults.standard.bool(forKey: Constants.AppStorageKeys.showConfidenceScores) }
    set {
      UserDefaults.standard.set(newValue, forKey: Constants.AppStorageKeys.showConfidenceScores)
    }
  }

  var frameProcessingInterval: Int {
    get { UserDefaults.standard.integer(forKey: Constants.AppStorageKeys.frameProcessingInterval) }
    set {
      UserDefaults.standard.set(newValue, forKey: Constants.AppStorageKeys.frameProcessingInterval)
    }
  }

  // MARK: - Model Settings
  private var _selectedModel: String? {
    get { UserDefaults.standard.string(forKey: Constants.AppStorageKeys.selectedModel) }
    set {
      UserDefaults.standard.set(newValue, forKey: Constants.AppStorageKeys.selectedModel)
    }
  }

  var selectedModel: String? {
    get { _selectedModel }
    set { _selectedModel = newValue }
  }

  // MARK: - Initialization
  private init() {
    // Private initializer to enforce singleton pattern
    setupDefaultValues()
  }

  private func setupDefaultValues() {
    // Set default values if they haven't been set yet
    if UserDefaults.standard.object(forKey: Constants.AppStorageKeys.isDetectionEnabled) == nil {
      UserDefaults.standard.set(
        Constants.DefaultSettings.isDetectionEnabled,
        forKey: Constants.AppStorageKeys.isDetectionEnabled)
    }

    if UserDefaults.standard.object(forKey: Constants.AppStorageKeys.confidenceThreshold) == nil {
      UserDefaults.standard.set(
        Constants.DefaultSettings.confidenceThreshold,
        forKey: Constants.AppStorageKeys.confidenceThreshold)
    }

    if UserDefaults.standard.object(forKey: Constants.AppStorageKeys.showConfidenceScores) == nil {
      UserDefaults.standard.set(
        Constants.DefaultSettings.showConfidenceScores,
        forKey: Constants.AppStorageKeys.showConfidenceScores)
    }

    if UserDefaults.standard.object(forKey: Constants.AppStorageKeys.frameProcessingInterval) == nil
    {
      UserDefaults.standard.set(
        Constants.DefaultSettings.frameProcessingInterval,
        forKey: Constants.AppStorageKeys.frameProcessingInterval)
    }
  }

  // MARK: - Public Methods

  /// Resets all settings to their default values
  func resetToDefaults() {
    isDetectionEnabled = Constants.DefaultSettings.isDetectionEnabled
    confidenceThreshold = Constants.DefaultSettings.confidenceThreshold
    showConfidenceScores = Constants.DefaultSettings.showConfidenceScores
    frameProcessingInterval = Constants.DefaultSettings.frameProcessingInterval
    selectedModel = Constants.DefaultSettings.selectedModel
  }

  // MARK: - Computed Properties for UI

  /// Returns confidence threshold as percentage string
  var confidenceThresholdPercentage: String {
    return confidenceThreshold.formatted(.percent)
  }

  /// Returns frame processing interval description
  var frameProcessingDescription: String {
    switch frameProcessingInterval {
    case 1:
      return "Every frame (High CPU usage)"
    case 2...5:
      return "Every \(frameProcessingInterval) frames (Balanced)"
    case 6...10:
      return "Every \(frameProcessingInterval) frames (Low CPU usage)"
    default:
      return "Every \(frameProcessingInterval) frames"
    }
  }
}

// MARK: - Settings Sections for UI
extension AppSettings {

  /// Detection settings section data for UI
  var detectionSettings: [(String, String, Any)] {
    return [
      ("Detection Enabled", "Enable/disable object detection", isDetectionEnabled),
      ("Confidence Threshold", confidenceThresholdPercentage, confidenceThreshold),
      ("Show Confidence Scores", "Display confidence percentages", showConfidenceScores),
      ("Processing Interval", frameProcessingDescription, frameProcessingInterval),
    ]
  }
}
