import AVFoundation
import Combine
import UIKit

/// Manages camera capture session and video output
class CameraService: NSObject, ObservableObject {

  // MARK: - Published Properties
  @Published var isSessionRunning = false
  @Published var cameraPermissionStatus: AVAuthorizationStatus = .notDetermined
  @Published var error: CameraError?

  // MARK: - Private Properties
  private let captureSession = AVCaptureSession()
  private var videoOutput: AVCaptureVideoDataOutput?
  private var captureDevice: AVCaptureDevice?
  private let sessionQueue = DispatchQueue(label: "camera.session.queue")
  private let videoOutputQueue = DispatchQueue(label: "camera.video.output.queue")

  // MARK: - Delegates
  weak var frameDelegate: CameraFrameDelegate?

  // MARK: - Initialization
  override init() {
    super.init()
    checkCameraPermission()
  }

  // MARK: - Public Methods

  /// Starts the camera capture session
  func startSession() {
    sessionQueue.async { [weak self] in
      guard let self = self else { return }

      if self.cameraPermissionStatus == .authorized {
        self.setupCaptureSession()
        self.captureSession.startRunning()

        Task { @MainActor in self.isSessionRunning = self.captureSession.isRunning }
      } else {
        Task { @MainActor in self.error = .permissionDenied }
      }
    }
  }

  /// Stops the camera capture session
  func stopSession() {
    sessionQueue.async { [weak self] in
      guard let self = self else { return }

      if self.captureSession.isRunning {
        self.captureSession.stopRunning()

        Task { @MainActor in self.isSessionRunning = false }
      }
    }
  }

  /// Requests camera permission
  func requestCameraPermission() {
    Task {
      let granted = await AVCaptureDevice.requestAccess(for: .video)
      await MainActor.run {
        self.cameraPermissionStatus = granted ? .authorized : .denied
        if !granted { self.error = .permissionDenied }
      }
      if granted { startSession() }
    }
  }

  /// Returns the capture session for preview layer
  func getCaptureSession() -> AVCaptureSession {
    return captureSession
  }

  // MARK: - Private Methods

  private func checkCameraPermission() {
    cameraPermissionStatus = AVCaptureDevice.authorizationStatus(for: .video)
  }

  private func setupCaptureSession() {
    captureSession.beginConfiguration()

    // Configure session preset
    if captureSession.canSetSessionPreset(.high) {
      captureSession.sessionPreset = .high
    }

    // Setup camera input
    setupCameraInput()

    // Setup video output
    setupVideoOutput()

    captureSession.commitConfiguration()
  }

  private func setupCameraInput() {
    // Remove existing inputs
    captureSession.inputs.forEach { captureSession.removeInput($0) }

    // Get back camera
    guard
      let camera = AVCaptureDevice.default(.builtInWideAngleCamera, for: .video, position: .back)
    else {
      Task { @MainActor in self.error = .cameraNotAvailable }
      return
    }

    self.captureDevice = camera

    do {
      let cameraInput = try AVCaptureDeviceInput(device: camera)

      if captureSession.canAddInput(cameraInput) {
        captureSession.addInput(cameraInput)
      } else {
        Task { @MainActor in self.error = .configurationFailed }
      }
    } catch {
      Task { @MainActor in self.error = .configurationFailed }
    }
  }

  private func setupVideoOutput() {
    // Remove existing outputs
    captureSession.outputs.forEach { captureSession.removeOutput($0) }

    let videoOutput = AVCaptureVideoDataOutput()
    videoOutput.setSampleBufferDelegate(self, queue: videoOutputQueue)
    videoOutput.alwaysDiscardsLateVideoFrames = true

    // Configure video settings
    videoOutput.videoSettings = [
      kCVPixelBufferPixelFormatTypeKey as String: kCVPixelFormatType_32BGRA
    ]

    if captureSession.canAddOutput(videoOutput) {
      captureSession.addOutput(videoOutput)
      self.videoOutput = videoOutput

      // Configure video orientation
      if let connection = videoOutput.connection(with: .video) {
        let portraitAngle: CGFloat = 90  // 90 degrees for portrait orientation
        if connection.isVideoRotationAngleSupported(portraitAngle) {
          connection.videoRotationAngle = portraitAngle
        }
        if connection.isVideoMirroringSupported {
          connection.isVideoMirrored = false
        }
      }
    } else {
      Task { @MainActor in self.error = .configurationFailed }
    }
  }
}

// MARK: - AVCaptureVideoDataOutputSampleBufferDelegate
extension CameraService: AVCaptureVideoDataOutputSampleBufferDelegate {

  func captureOutput(
    _ output: AVCaptureOutput, didOutput sampleBuffer: CMSampleBuffer,
    from connection: AVCaptureConnection
  ) {
    // Forward frame to delegate for processing
    guard let imageBuffer = CMSampleBufferGetImageBuffer(sampleBuffer) else {
      return
    }
    frameDelegate?.didReceiveFrame(imageBuffer)
  }

  func captureOutput(
    _ output: AVCaptureOutput, didDrop sampleBuffer: CMSampleBuffer,
    from connection: AVCaptureConnection
  ) {
    // Handle dropped frames if needed
    print("Dropped frame")
  }
}

// MARK: - Supporting Types

/// Delegate protocol for receiving camera frames
protocol CameraFrameDelegate: AnyObject {
  func didReceiveFrame(_ imageBuffer: CVImageBuffer)
}

/// Camera-related errors
enum CameraError: LocalizedError {
  case permissionDenied
  case cameraNotAvailable
  case configurationFailed

  var errorDescription: String? {
    switch self {
    case .permissionDenied:
      return Constants.ErrorMessages.cameraPermissionDenied
    case .cameraNotAvailable:
      return Constants.ErrorMessages.cameraNotAvailable
    case .configurationFailed:
      return "Failed to configure camera session."
    }
  }
}
