import Combine
import CoreML
import Foundation
import SwiftUI

/// Manages CoreML model downloads, storage, and metadata
class ModelManagerService: NSObject, ObservableObject {

  // MARK: - AppStorage Properties
  @AppStorage(Constants.AppStorageKeys.selectedModel) private var selectedModel: String?

  // MARK: - Published Properties
  @Published var availableModels: [MLModelInfo] = []
  @Published var downloadProgress: [String: Double] = [:]
  @Published var isLoading = false
  @Published var error: ModelManagerError?

  // MARK: - Private Properties
  private let fileManager = FileManager.default
  private var downloadTasks: [String: URLSessionDownloadTask] = [:]
  private lazy var urlSession: URLSession = {
    let config = URLSessionConfiguration.background(
      withIdentifier: "com.ssv.ai-detection.downloader")
    config.isDiscretionary = false
    config.sessionSendsLaunchEvents = true
    config.httpAdditionalHeaders = ["User-Agent": ""]
    config.waitsForConnectivity = true
    return URLSession(configuration: config, delegate: self, delegateQueue: nil)
  }()
  private var isPreview: Bool {
    #if DEBUG
      return ProcessInfo.processInfo.environment["XCODE_RUNNING_FOR_PREVIEWS"] == "1"
    #else
      return false
    #endif
  }

  // MARK: - Initialization
  override init() {
    super.init()
    Task { await loadAvailableModels() }
  }

  // MARK: - Public Methods

  /// Loads all available models
  func loadAvailableModels() async {
    await MainActor.run { self.isLoading = true }

    let models: [MLModelInfo] = isPreview ? MLModelInfo.mock : await loadModels()

    if isPreview {
      downloadProgress[models.first!.name] = 0.6
    }

    await MainActor.run {
      self.availableModels = models.sorted { $0.name < $1.name }
      self.isLoading = false
    }
  }

  /// Downloads a model from URL
  func downloadModel(_ model: MLModelInfo) async {
    guard let downloadURL = URL(string: model.downloadURL) else {
      await MainActor.run { self.error = .invalidURL }
      return
    }

    // Check available storage
    let estimatedSize = model.fileSize
    if estimatedSize > 0 && !hasEnoughStorage(for: estimatedSize) {
      await MainActor.run { self.error = .insufficientStorage }
      return
    }

    await MainActor.run { self.downloadProgress[model.name] = 0.0 }

    let downloadTask = urlSession.downloadTask(with: downloadURL)

    downloadTasks[model.name] = downloadTask
    downloadTask.resume()
  }

  /// Cancels a model download
  func cancelDownload(for name: String) {
    downloadTasks[name]?.cancel()
    downloadTasks.removeValue(forKey: name)
    downloadProgress.removeValue(forKey: name)
  }

  /// Deletes a downloaded model
  func deleteModel(_ model: MLModelInfo) async {
    guard let filePath = model.filePath else {
      return
    }

    do {
      let fileURL = URL(fileURLWithPath: filePath)
      try fileManager.removeItem(at: fileURL)
    } catch {
      self.error = .deletionFailed(error.localizedDescription)
    }

    await loadAvailableModels()
  }

  /// Returns the selected model
  func getSelectedModel() -> MLModelInfo? {
    return availableModels.first { $0.name == selectedModel }
  }

  /// Sets the selected model
  func setSelectedModel(_ model: MLModelInfo) {
    selectedModel = model.name
  }

  // MARK: - Private Methods

  private func loadModels() async -> [MLModelInfo] {
    guard
      let contents = try? fileManager.contentsOfDirectory(
        at: fileManager.modelsDirectory,
        includingPropertiesForKeys: nil,
      )
    else { return [] }

    let paths = contents.filter { fileURL in
      fileURL.pathExtension == Constants.compiledModelExtension
    }
    return [
      MLModelInfo(
        name: "YOLOv3",
        downloadURL:
          "https://ml-assets.apple.com/coreml/models/Image/ObjectDetection/YOLOv3/YOLOv3.mlmodel",
        fileSize: 248_400_000
      ),
      MLModelInfo(
        name: "YOLOv3FP16",
        downloadURL:
          "https://ml-assets.apple.com/coreml/models/Image/ObjectDetection/YOLOv3/YOLOv3FP16.mlmodel",
        fileSize: 124_200_000
      ),
      MLModelInfo(
        name: "YOLOv3Int8LUT",
        downloadURL:
          "https://ml-assets.apple.com/coreml/models/Image/ObjectDetection/YOLOv3/YOLOv3Int8LUT.mlmodel",
        fileSize: 62_200_000
      ),
      MLModelInfo(
        name: "YOLOv3Tiny",
        downloadURL:
          "https://ml-assets.apple.com/coreml/models/Image/ObjectDetection/YOLOv3Tiny/YOLOv3Tiny.mlmodel",
        fileSize: 35_400_000
      ),
      MLModelInfo(
        name: "YOLOv3TinyFP16",
        downloadURL:
          "https://ml-assets.apple.com/coreml/models/Image/ObjectDetection/YOLOv3Tiny/YOLOv3TinyFP16.mlmodel",
        fileSize: 17_700_000
      ),
      MLModelInfo(
        name: "YOLOv3TinyInt8LUT",
        downloadURL:
          "https://ml-assets.apple.com/coreml/models/Image/ObjectDetection/YOLOv3Tiny/YOLOv3TinyInt8LUT.mlmodel",
        fileSize: 8_900_000
      ),
    ].map { model in
      model.copy(
        filePath: paths.first { $0.lastPathComponent.withoutExtension == model.name }?.path,
      )
    }
  }

  private func hasEnoughStorage(for fileSize: Int64) -> Bool {
    let availableSpace = fileManager.availableDiskSpace
    let requiredSpace = fileSize + (100 * 1024 * 1024)  // Add 100MB buffer
    return availableSpace >= requiredSpace
  }
}

// MARK: - URLSessionDownloadDelegate
extension ModelManagerService: URLSessionDownloadDelegate {

  func urlSession(
    _ session: URLSession,
    downloadTask: URLSessionDownloadTask,
    didWriteData bytesWritten: Int64,
    totalBytesWritten: Int64,
    totalBytesExpectedToWrite: Int64
  ) {
    let name = downloadTasks.first { $0.value == downloadTask }?.key
    guard let name = name, totalBytesExpectedToWrite > 0 else { return }

    let progress = Double(totalBytesWritten) / Double(totalBytesExpectedToWrite)

    Task { @MainActor in self.downloadProgress[name] = progress }
  }

  func urlSession(
    _ session: URLSession,
    downloadTask: URLSessionDownloadTask,
    didFinishDownloadingTo location: URL
  ) {
    let name = downloadTasks.first { $0.value == downloadTask }?.key
    guard let response = downloadTask.response as? HTTPURLResponse else {
      Task { @MainActor in self.error = .downloadFailed("No HTTP Result") }
      return
    }
    guard response.statusCode == 200, let name = name else {
      Task { @MainActor in self.error = .downloadFailed("No file received") }
      return
    }

    let tempDirectory = fileManager.temporaryDirectory
    let tempDestinationURL = tempDirectory.appendingPathComponent("\(name).mlmodel")

    do {
      if fileManager.fileExists(atPath: tempDestinationURL.path) {
        try fileManager.removeItem(at: tempDestinationURL)
      }
      try fileManager.moveItem(at: location, to: tempDestinationURL)

      Task {
        do {
          guard let compiledURL = try? await MLModel.compileModel(at: tempDestinationURL) else {
            await MainActor.run { self.error = .modelValidationFailed }
            return
          }

          let modelsDirectory = fileManager.modelsDirectory
          let destinationURL = modelsDirectory.appendingPathComponent("\(name).mlmodelc")

          if fileManager.fileExists(atPath: destinationURL.path) {
            try fileManager.removeItem(at: destinationURL)
          }

          try fileManager.moveItem(at: compiledURL, to: destinationURL)

          await MainActor.run {
            self.downloadProgress.removeValue(forKey: name)
            self.downloadTasks.removeValue(forKey: name)
          }

          await loadAvailableModels()
        } catch {
          await MainActor.run {
            self.error = .downloadFailed("Failed to save model: \(error.localizedDescription)")
          }
        }

        if fileManager.fileExists(atPath: tempDestinationURL.path) {
          try fileManager.removeItem(at: tempDestinationURL)
        }
      }
    } catch {
      Task { @MainActor in
        self.error = .downloadFailed("Failed to save model: \(error.localizedDescription)")
      }
    }
  }

  func urlSession(_ session: URLSession, task: URLSessionTask, didCompleteWithError error: Error?) {
    if let error = error {
      Task { @MainActor in self.error = .downloadFailed(error.localizedDescription) }
      return
    }
  }
}

// MARK: - Supporting Types

/// Model manager related errors
enum ModelManagerError: LocalizedError {
  case invalidURL
  case downloadFailed(String)
  case deletionFailed(String)
  case insufficientStorage
  case modelValidationFailed

  var errorDescription: String? {
    switch self {
    case .invalidURL:
      return "Invalid model download URL."
    case .downloadFailed(let message):
      return "Download failed: \(message)"
    case .deletionFailed(let message):
      return "Failed to delete model: \(message)"
    case .insufficientStorage:
      return Constants.ErrorMessages.insufficientStorage
    case .modelValidationFailed:
      return "Model validation failed."
    }
  }
}
