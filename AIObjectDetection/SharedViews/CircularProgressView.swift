import SwiftUI

struct CircularProgressView: View {
  @State private var isAnimating = false

  private let value: CGFloat?
  private let lineWidth: CGFloat

  init(value: CGFloat? = nil, lineWidth: CGFloat = 2) {
    self.value = value
    self.lineWidth = lineWidth
  }

  var body: some View {
    if value != nil {
      ZStack {
        Circle()
          .stroke(
            Color.secondary.opacity(0.3),
            style: StrokeStyle(lineWidth: lineWidth)
          )

        circle()
          .rotationEffect(.degrees(270))
      }
    } else {
      circle()
        .rotationEffect(.degrees(isAnimating ? 360 : 0))
        .animation(
          .linear(duration: 1.0).repeatForever(autoreverses: false),
          value: isAnimating
        )
        .onAppear {
          isAnimating = true
        }
    }
  }

  @ViewBuilder
  private func circle() -> some View {
    Circle()
      .trim(from: 0, to: value ?? 0.7)
      .stroke(
        Color.accentColor,
        style: StrokeStyle(lineWidth: lineWidth)
      )
  }
}

#Preview {
  CircularProgressView().frame(width: 36, height: 36)
  CircularProgressView(value: 0.1).frame(width: 36, height: 36)
}
