import Foundation

struct Constants {

  // MARK: - Detection Settings
  static let defaultConfidenceThreshold = 0.5
  static let frameProcessingInterval = 5  // Process every 5th frame
  static let maxDetectionsPerFrame = 10

  // MARK: - Model Management
  static let modelsDirectoryName = "MLModels"
  static let compiledModelExtension = "mlmodelc"

  // MARK: - Detection Box Styling
  static let detectionBoxLineWidth: CGFloat = 2.0
  static let detectionLabelFontSize: CGFloat = 14
  static let detectionLabelPadding: CGFloat = 4

  // MARK: - AppStorage Keys
  struct AppStorageKeys {
    static let selectedModel = "selectedModel"
    static let confidenceThreshold = "confidenceThreshold"
    static let isDetectionEnabled = "isDetectionEnabled"
    static let frameProcessingInterval = "frameProcessingInterval"
    static let showConfidenceScores = "showConfidenceScores"
  }

  // MARK: - Error Messages
  struct ErrorMessages {
    static let cameraPermissionDenied =
      "Camera permission is required for object detection. Please enable it in Settings."
    static let cameraNotAvailable = "Camera is not available on this device."
    static let modelLoadingFailed =
      "Failed to load the selected model. Please try a different model."
    static let modelDownloadFailed =
      "Failed to download the model. Please check your internet connection."
    static let invalidModelFormat = "The selected model format is not supported."
    static let insufficientStorage = "Not enough storage space to download the model."
  }
}
