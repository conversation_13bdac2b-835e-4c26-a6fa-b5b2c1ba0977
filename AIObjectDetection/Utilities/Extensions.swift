import AVFoundation
import CoreGraphics
import SwiftUI

// MARK: - Color Extensions
extension Color {
  /// App-specific colors
  static let detectionBoxColor = Color(.systemGreen)
  static let highConfidenceColor = Color.green
  static let mediumConfidenceColor = Color.orange
  static let lowConfidenceColor = Color.red

  /// Returns color based on confidence level
  static func confidenceColor(for confidence: Float) -> Color {
    switch confidence {
    case 0.7...1.0:
      return .highConfidenceColor
    case 0.4..<0.7:
      return .mediumConfidenceColor
    default:
      return .lowConfidenceColor
    }
  }
}

// MARK: - String Extensions
extension String {
  /// Removes file extension from string
  var withoutExtension: String {
    return (self as NSString).deletingPathExtension
  }
}

// MARK: - URL Extensions
extension URL {
  /// Returns the file size in bytes
  var fileSize: Int64 {
    do {
      let resourceValues = try resourceValues(forKeys: [.fileSizeKey])
      return Int64(resourceValues.fileSize ?? 0)
    } catch {
      return 0
    }
  }
}

// MARK: - FileManager Extensions
extension FileManager {
  /// Returns the app's documents directory
  var documentsDirectory: URL {
    return urls(for: .documentDirectory, in: .userDomainMask).first!
  }

  /// Returns the app's support directory
  var supportDirectory: URL {
    let url = urls(for: .applicationSupportDirectory, in: .userDomainMask).first!
    try? createDirectory(at: url, withIntermediateDirectories: true)
    return url
  }

  /// Returns the models directory
  var modelsDirectory: URL {
    let url = supportDirectory.appendingPathComponent(Constants.modelsDirectoryName)
    try? createDirectory(at: url, withIntermediateDirectories: true)
    return url
  }

  /// Returns available disk space in bytes
  var availableDiskSpace: Int64 {
    do {
      let systemAttributes = try attributesOfFileSystem(forPath: NSHomeDirectory())
      return (systemAttributes[.systemFreeSize] as? NSNumber)?.int64Value ?? 0
    } catch {
      return 0
    }
  }
}
