import AVFoundation
import Combine
import Foundation
import SwiftUI

/// ViewModel for camera and detection functionality
class CameraViewModel: ObservableObject {

  // MARK: - AppStorage Properties
  @AppStorage(Constants.AppStorageKeys.isDetectionEnabled) var isDetectionEnabled: Bool = true {
    didSet {
      if !isDetectionEnabled { detectionService.clearDetections() }
    }
  }
  @AppStorage(Constants.AppStorageKeys.confidenceThreshold) var confidenceThreshold: Double =
    Constants.defaultConfidenceThreshold
  {
    didSet {
      if !isDetectionEnabled { detectionService.clearDetections() }
    }
  }
  @AppStorage(Constants.AppStorageKeys.showConfidenceScores) var showConfidenceScores: Bool = true

  // MARK: - Published Properties
  @Published var isSessionRunning = false
  @Published var cameraPermissionStatus: AVAuthorizationStatus = .notDetermined
  @Published var currentDetections: [DetectionResult] = []
  @Published var currentModel: MLModelInfo?
  @Published var isProcessing = false
  @Published var error: String?
  @Published var frameSize: CGSize = .zero

  // MARK: - Services
  private let cameraService = CameraService()
  private let detectionService = DetectionService()
  private let modelManager = ModelManagerService()

  // MARK: - Private Properties
  private var cancellables = Set<AnyCancellable>()

  // MARK: - Initialization
  init() {
    setupBindings()
    loadInitialModel()
  }

  // MARK: - Public Methods

  /// Starts the camera session
  func startCamera() {
    cameraService.startSession()
  }

  /// Stops the camera session
  func stopCamera() {
    cameraService.stopSession()
  }

  /// Requests camera permission
  func requestCameraPermission() {
    cameraService.requestCameraPermission()
  }

  /// Toggles confidence score display
  func toggleConfidenceScores() {
    showConfidenceScores.toggle()
  }

  /// Loads a new model for detection
  func loadModel(_ model: MLModelInfo) {
    Task {
      await detectionService.loadModel(model)
      await MainActor.run {
        self.currentModel = model
        self.modelManager.setSelectedModel(model)
      }
    }
  }

  /// Returns the camera capture session for preview
  func getCaptureSession() -> AVCaptureSession {
    return cameraService.getCaptureSession()
  }

  /// Clears current detections
  func clearDetections() {
    detectionService.clearDetections()
  }

  // MARK: - Private Methods

  private func setupBindings() {
    // Bind camera service properties
    cameraService.$isSessionRunning
      .receive(on: DispatchQueue.main)
      .assign(to: \.isSessionRunning, on: self)
      .store(in: &cancellables)

    cameraService.$cameraPermissionStatus
      .receive(on: DispatchQueue.main)
      .assign(to: \.cameraPermissionStatus, on: self)
      .store(in: &cancellables)

    cameraService.$error
      .receive(on: DispatchQueue.main)
      .map { $0?.localizedDescription }
      .assign(to: \.error, on: self)
      .store(in: &cancellables)

    // Bind detection service properties
    detectionService.$currentDetections
      .receive(on: DispatchQueue.main)
      .assign(to: \.currentDetections, on: self)
      .store(in: &cancellables)

    detectionService.$currentModel
      .receive(on: DispatchQueue.main)
      .assign(to: \.currentModel, on: self)
      .store(in: &cancellables)

    detectionService.$isProcessing
      .receive(on: DispatchQueue.main)
      .assign(to: \.isProcessing, on: self)
      .store(in: &cancellables)

    detectionService.$error
      .receive(on: DispatchQueue.main)
      .map { $0?.localizedDescription }
      .assign(to: \.error, on: self)
      .store(in: &cancellables)

    // Set camera service as frame delegate
    cameraService.frameDelegate = self
  }

  private func loadInitialModel() {
    Task {
      // Wait for model manager to load available models
      await modelManager.loadAvailableModels()

      await MainActor.run {
        // Try to load the previously selected model
        if let selectedModel = self.modelManager.getSelectedModel() {
          self.loadModel(selectedModel)
        }
      }
    }
  }
}

// MARK: - CameraFrameDelegate
extension CameraViewModel: CameraFrameDelegate {

  func didReceiveFrame(_ imageBuffer: CVImageBuffer) {
    // Update frame size for UI calculations
    let width = CVPixelBufferGetWidth(imageBuffer)
    let height = CVPixelBufferGetHeight(imageBuffer)

    Task { @MainActor in self.frameSize = CGSize(width: width, height: height) }

    // Process frame for detection
    detectionService.processFrame(imageBuffer)
  }
}
