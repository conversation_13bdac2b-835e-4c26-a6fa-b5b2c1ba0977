import AVFoundation
import Combine

/// ViewModel for camera and detection functionality
@Observable
class CameraViewModel {

  // MARK: - Settings Manager
  var appSettings = AppSettings.shared

  // MARK: - Observable Properties
  var cameraPermissionStatus: AVAuthorizationStatus = .notDetermined
  var currentDetections: [DetectionResult] = []
  var currentModel: MLModelInfo?
  var isProcessing = false
  var error: String?
  var frameSize: CGSize = .zero

  // MARK: - Services
  private let cameraService = CameraService()
  private let detectionService = DetectionService()
  private let modelManager = ModelManagerService()

  // MARK: - Private Properties
  private var cancellables = Set<AnyCancellable>()

  // MARK: - Initialization
  init() {
    setupBindings()
    setupSettingsObservation()
    loadInitialModel()
  }

  // MARK: - Public Methods

  /// Starts the camera session
  func startCamera() {
    cameraService.startSession()
  }

  /// Stops the camera session
  func stopCamera() {
    cameraService.stopSession()
  }

  /// Requests camera permission
  func requestCameraPermission() {
    cameraService.requestCameraPermission()
  }

  /// Toggles confidence score display
  func toggleConfidenceScores() {
    appSettings.showConfidenceScores.toggle()
  }

  /// Loads a new model for detection
  func loadModel(_ model: MLModelInfo) {
    Task {
      await detectionService.loadModel(model)
      await MainActor.run {
        self.currentModel = model
        self.modelManager.setSelectedModel(model)
      }
    }
  }

  /// Returns the camera capture session for preview
  func getCaptureSession() -> AVCaptureSession {
    return cameraService.getCaptureSession()
  }

  /// Clears current detections
  func clearDetections() {
    detectionService.clearDetections()
  }

  // MARK: - Settings Access

  /// Access to detection enabled setting
  var isDetectionEnabled: Bool {
    get { appSettings.isDetectionEnabled }
    set { appSettings.isDetectionEnabled = newValue }
  }

  /// Access to confidence threshold setting
  var confidenceThreshold: Double {
    get { appSettings.confidenceThreshold }
    set { appSettings.confidenceThreshold = newValue }
  }

  /// Access to show confidence scores setting
  var showConfidenceScores: Bool {
    get { appSettings.showConfidenceScores }
    set { appSettings.showConfidenceScores = newValue }
  }

  // MARK: - Private Methods

  private func setupBindings() {
    // Bind camera service properties
    cameraService.$cameraPermissionStatus
      .receive(on: DispatchQueue.main)
      .assign(to: \.cameraPermissionStatus, on: self)
      .store(in: &cancellables)

    cameraService.$error
      .receive(on: DispatchQueue.main)
      .map { $0?.localizedDescription }
      .assign(to: \.error, on: self)
      .store(in: &cancellables)

    // Bind detection service properties
    detectionService.$currentDetections
      .receive(on: DispatchQueue.main)
      .assign(to: \.currentDetections, on: self)
      .store(in: &cancellables)

    detectionService.$currentModel
      .receive(on: DispatchQueue.main)
      .assign(to: \.currentModel, on: self)
      .store(in: &cancellables)

    detectionService.$isProcessing
      .receive(on: DispatchQueue.main)
      .assign(to: \.isProcessing, on: self)
      .store(in: &cancellables)

    detectionService.$error
      .receive(on: DispatchQueue.main)
      .map { $0?.localizedDescription }
      .assign(to: \.error, on: self)
      .store(in: &cancellables)

    // Set camera service as frame delegate
    cameraService.frameDelegate = self
  }

  private func setupSettingsObservation() {
    // With @Observable, we don't need explicit objectWillChange handling
    // The observation will happen automatically when appSettings properties change
    // We just need to monitor for specific business logic like clearing detections

    // Note: In a real implementation, you might want to use withObservationTracking
    // or other observation mechanisms if you need to react to specific changes
  }

  private func loadInitialModel() {
    Task {
      // Wait for model manager to load available models
      await modelManager.loadAvailableModels()

      await MainActor.run {
        // Try to load the previously selected model
        if let selectedModel = self.modelManager.getSelectedModel() {
          self.loadModel(selectedModel)
        }
      }
    }
  }
}

// MARK: - CameraFrameDelegate
extension CameraViewModel: CameraFrameDelegate {

  func didReceiveFrame(_ imageBuffer: CVImageBuffer) {
    // Update frame size for UI calculations
    let width = CVPixelBufferGetWidth(imageBuffer)
    let height = CVPixelBufferGetHeight(imageBuffer)

    Task { @MainActor in self.frameSize = CGSize(width: width, height: height) }

    // Process frame for detection
    detectionService.processFrame(imageBuffer)
  }
}
