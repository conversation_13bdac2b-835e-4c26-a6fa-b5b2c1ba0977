import Combine
import Foundation

/// ViewModel for managing CoreML models
class ModelManagerViewModel: ObservableObject {

  // MARK: - Published Properties
  @Published var availableModels: [MLModelInfo] = []
  @Published var downloadProgress: [String: Double] = [:]
  @Published var isLoading = false
  @Published var error: String?
  @Published var selectedModel: MLModelInfo?

  // MARK: - Services
  private let modelManager = ModelManagerService()

  // MARK: - Private Properties
  private var cancellables = Set<AnyCancellable>()

  // MARK: - Initialization
  init() {
    setupBindings()
    Task { await loadModels() }
  }

  // MARK: - Public Methods

  /// Refreshes the list of available models
  func refreshModels() {
    Task { await modelManager.loadAvailableModels() }
  }

  /// Downloads a new model from URL
  func downloadModel(_ model: MLModelInfo) {
    Task { await modelManager.downloadModel(model) }
  }

  /// Cancels a model download
  func cancelDownload(for name: String) {
    modelManager.cancelDownload(for: name)
  }

  /// Deletes a downloaded model
  func deleteModel(_ model: MLModelInfo) {
    Task { await modelManager.deleteModel(model) }
  }

  /// Selects a model for detection
  func selectModel(_ model: MLModelInfo) {
    selectedModel = model
    modelManager.setSelectedModel(model)
  }

  /// Returns whether a model is currently downloading
  func isDownloading(_ model: MLModelInfo) -> Bool {
    return downloadProgress.keys.contains(model.name)
  }

  /// Returns download progress for a model
  func getDownloadProgress(for model: MLModelInfo) -> Double {
    return downloadProgress[model.name] ?? 0.0
  }

  // MARK: - Private Methods

  private func setupBindings() {
    // Bind model manager properties
    modelManager.$availableModels
      .receive(on: DispatchQueue.main)
      .assign(to: \.availableModels, on: self)
      .store(in: &cancellables)

    modelManager.$downloadProgress
      .receive(on: DispatchQueue.main)
      .assign(to: \.downloadProgress, on: self)
      .store(in: &cancellables)

    modelManager.$isLoading
      .receive(on: DispatchQueue.main)
      .assign(to: \.isLoading, on: self)
      .store(in: &cancellables)

    modelManager.$error
      .receive(on: DispatchQueue.main)
      .map { $0?.localizedDescription }
      .assign(to: \.error, on: self)
      .store(in: &cancellables)

    // Load selected model
    loadSelectedModel()
  }

  private func loadModels() async {
    await modelManager.loadAvailableModels()
  }

  private func loadSelectedModel() {
    selectedModel = modelManager.getSelectedModel()
  }
}
