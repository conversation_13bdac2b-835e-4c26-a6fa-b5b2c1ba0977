import Combine
import Foundation

/// ViewModel for managing CoreML models
@Observable
class ModelManagerViewModel {

  // MARK: - Observable Properties
  var availableModels: [MLModelInfo] = []
  var downloadProgress: [String: Double] = [:]
  var isLoading = false
  var error: String?
  var selectedModel: MLModelInfo?

  // MARK: - Services
  private let modelManager = ModelManagerService()

  // MARK: - Initialization
  init() {
    Task {
      await loadModels()
      await MainActor.run {
        // Sync properties from service
        self.availableModels = modelManager.availableModels
        self.downloadProgress = modelManager.downloadProgress
        self.isLoading = modelManager.isLoading
        self.error = modelManager.error?.localizedDescription
        self.selectedModel = modelManager.getSelectedModel()
      }
    }
  }

  // MARK: - Public Methods

  /// Refreshes the list of available models
  func refreshModels() {
    Task {
      await modelManager.loadAvailableModels()
      await MainActor.run { syncFromService() }
    }
  }

  /// Downloads a new model from URL
  func downloadModel(_ model: MLModelInfo) {
    Task { await modelManager.downloadModel(model) }
  }

  /// Cancels a model download
  func cancelDownload(for name: String) {
    modelManager.cancelDownload(for: name)
  }

  /// Deletes a downloaded model
  func deleteModel(_ model: MLModelInfo) {
    Task { await modelManager.deleteModel(model) }
  }

  /// Selects a model for detection
  func selectModel(_ model: MLModelInfo) {
    selectedModel = model
    modelManager.setSelectedModel(model)
  }

  /// Returns whether a model is currently downloading
  func isDownloading(_ model: MLModelInfo) -> Bool {
    return downloadProgress.keys.contains(model.name)
  }

  /// Returns download progress for a model
  func getDownloadProgress(for model: MLModelInfo) -> Double {
    return downloadProgress[model.name] ?? 0.0
  }

  // MARK: - Private Methods

  private func loadModels() async {
    await modelManager.loadAvailableModels()
  }

  private func loadSelectedModel() {
    selectedModel = modelManager.getSelectedModel()
  }

  /// Syncs properties from the service to the view model
  private func syncFromService() {
    availableModels = modelManager.availableModels
    downloadProgress = modelManager.downloadProgress
    isLoading = modelManager.isLoading
    error = modelManager.error?.localizedDescription
    selectedModel = modelManager.getSelectedModel()
  }
}
