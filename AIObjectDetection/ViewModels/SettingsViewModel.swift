import Combine
import Foundation
import SwiftUI

/// ViewModel for app settings and preferences
class SettingsViewModel: ObservableObject {

  // MARK: - AppStorage Properties
  @AppStorage(Constants.AppStorageKeys.confidenceThreshold) var confidenceThreshold: Double =
    Constants.defaultConfidenceThreshold
  @AppStorage(Constants.AppStorageKeys.frameProcessingInterval) var frameProcessingInterval: Int =
    Constants.frameProcessingInterval
  @AppStorage(Constants.AppStorageKeys.showConfidenceScores) var showConfidenceScores: Bool = true
  @AppStorage(Constants.AppStorageKeys.isDetectionEnabled) var isDetectionEnabled: Bool = true

  // MARK: - Published Properties
  @Published var maxDetectionsPerFrame: Int = Constants.maxDetectionsPerFrame

  // MARK: - Storage Info
  @Published var availableStorage: String = ""
  @Published var usedModelStorage: String = ""

  // MARK: - Private Properties
  private var cancellables = Set<AnyCancellable>()
  private let fileManager = FileManager.default

  // MARK: - Initialization
  init() {
    updateStorageInfo()
  }

  // MARK: - Public Methods

  /// Resets settings to default values
  func resetToDefaults() {
    confidenceThreshold = Constants.defaultConfidenceThreshold
    frameProcessingInterval = Constants.frameProcessingInterval
    showConfidenceScores = true
    isDetectionEnabled = true
    maxDetectionsPerFrame = Constants.maxDetectionsPerFrame
  }

  /// Updates storage information
  func updateStorageInfo() {
    Task {
      let available = await getAvailableStorageString()
      let used = await getUsedModelStorageString()

      await MainActor.run {
        self.availableStorage = available
        self.usedModelStorage = used
      }
    }
  }

  /// Clears all downloaded models
  func clearAllDownloadedModels() {
    let modelsDirectory = fileManager.modelsDirectory

    do {
      let contents = try fileManager.contentsOfDirectory(
        at: modelsDirectory, includingPropertiesForKeys: nil)

      for fileURL in contents {
        try fileManager.removeItem(at: fileURL)
      }

      updateStorageInfo()
    } catch {
      print("Failed to clear downloaded models: \(error)")
    }
  }

  /// Returns confidence threshold as percentage string
  var confidenceThresholdPercentage: String {
    return confidenceThreshold.formatted(.percent)
  }

  /// Returns frame processing interval description
  var frameProcessingDescription: String {
    switch frameProcessingInterval {
    case 1:
      return "Every frame (High CPU usage)"
    case 2...5:
      return "Every \(frameProcessingInterval) frames (Balanced)"
    case 6...10:
      return "Every \(frameProcessingInterval) frames (Low CPU usage)"
    default:
      return "Every \(frameProcessingInterval) frames"
    }
  }

  /// Returns max detections description
  var maxDetectionsDescription: String {
    return "Show up to \(maxDetectionsPerFrame) detections per frame"
  }

  // MARK: - Private Methods

  private func getAvailableStorageString() async -> String {
    let availableBytes = fileManager.availableDiskSpace
    return availableBytes.formatted(
      .byteCount(style: .file, allowedUnits: [.kb, .mb, .gb], spellsOutZero: true))
  }

  private func getUsedModelStorageString() async -> String {
    let modelsDirectory = fileManager.modelsDirectory
    var totalSize: Int64 = 0

    do {
      let contents = try fileManager.contentsOfDirectory(
        at: modelsDirectory, includingPropertiesForKeys: [.fileSizeKey])

      for fileURL in contents {
        totalSize += fileURL.fileSize
      }
    } catch {
      print("Failed to calculate model storage: \(error)")
    }

    return totalSize.formatted(
      .byteCount(style: .file, allowedUnits: [.kb, .mb, .gb], spellsOutZero: true))
  }
}

// MARK: - Settings Sections
extension SettingsViewModel {

  /// Detection settings section
  var detectionSettings: [(String, String, Any)] {
    return [
      ("Detection Enabled", "Enable/disable object detection", isDetectionEnabled),
      ("Confidence Threshold", confidenceThresholdPercentage, confidenceThreshold),
      ("Show Confidence Scores", "Display confidence percentages", showConfidenceScores),
      ("Processing Interval", frameProcessingDescription, frameProcessingInterval),
      ("Max Detections", maxDetectionsDescription, maxDetectionsPerFrame),
    ]
  }
}
