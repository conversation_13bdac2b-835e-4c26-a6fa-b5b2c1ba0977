import Foundation

/// ViewModel for app settings and preferences
@Observable
class SettingsViewModel {

  // MARK: - Settings Manager
  var appSettings = AppSettings.shared

  // MARK: - Private Properties
  private let fileManager = FileManager.default

  // MARK: - Initialization
  init() {
    // With @Observable, no explicit binding setup needed
  }

  // MARK: - Public Methods

  /// Resets settings to default values
  func resetToDefaults() {
    appSettings.resetToDefaults()
  }

  /// Clears all downloaded models
  func clearAllDownloadedModels() {
    let modelsDirectory = fileManager.modelsDirectory

    do {
      let contents = try fileManager.contentsOfDirectory(
        at: modelsDirectory, includingPropertiesForKeys: nil)

      for fileURL in contents {
        try fileManager.removeItem(at: fileURL)
      }
    } catch {
      print("Failed to clear downloaded models: \(error)")
    }
  }

  /// Returns confidence threshold as percentage string
  var confidenceThresholdPercentage: String {
    return appSettings.confidenceThresholdPercentage
  }

  /// Returns frame processing interval description
  var frameProcessingDescription: String {
    return appSettings.frameProcessingDescription
  }
}

// MARK: - Settings Sections
extension SettingsViewModel {

  /// Detection settings section
  var detectionSettings: [(String, String, Any)] {
    return appSettings.detectionSettings
  }
}
