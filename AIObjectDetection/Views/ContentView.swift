import SwiftUI

struct ContentView: View {
  @State private var appSettings = AppSettings.shared
  @State private var cameraViewModel = CameraViewModel()
  @State private var modelManagerViewModel = ModelManagerViewModel()
  @State private var settingsViewModel = SettingsViewModel()

  @State private var selectedTab = 0

  var body: some View {
    TabView(selection: $selectedTab) {
      // Camera Tab
      CameraView()
        .environment(cameraViewModel)
        .environment(appSettings)
        .tabItem {
          Image(systemName: "camera.viewfinder")
          Text("Camera")
        }
        .tag(0)

      // Models Tab
      ModelListView()
        .environment(modelManagerViewModel)
        .environment(cameraViewModel)
        .environment(appSettings)
        .tabItem {
          Image(systemName: "brain.head.profile")
          Text("Models")
        }
        .tag(1)

      // Settings Tab
      SettingsView()
        .environment(settingsViewModel)
        .environment(cameraViewModel)
        .environment(appSettings)
        .tabItem {
          Image(systemName: "gear")
          Text("Settings")
        }
        .tag(2)
    }
    .onAppear {
      setupAppearance()
    }
  }

  private func setupAppearance() {
    // Configure tab bar appearance
    let tabBarAppearance = UITabBarAppearance()
    tabBarAppearance.configureWithOpaqueBackground()
    UITabBar.appearance().standardAppearance = tabBarAppearance
    UITabBar.appearance().scrollEdgeAppearance = tabBarAppearance
  }
}
