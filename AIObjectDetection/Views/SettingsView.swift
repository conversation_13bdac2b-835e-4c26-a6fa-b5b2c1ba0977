import SwiftUI

struct SettingsView: View {
  @EnvironmentObject var viewModel: SettingsViewModel
  @EnvironmentObject var cameraViewModel: CameraViewModel
  @State private var showingResetAlert = false
  @State private var showingClearModelsAlert = false

  var body: some View {
    NavigationView {
      List {
        // MARK: - Detection Settings Section
        Section("Detection Settings") {
          HStack {
            Label("Detection Enabled", systemImage: "viewfinder")
            Spacer()
            Toggle(
              "",
              isOn: Binding(
                get: { viewModel.isDetectionEnabled },
                set: { newValue in
                  viewModel.isDetectionEnabled = newValue
                  cameraViewModel.isDetectionEnabled = newValue
                }
              ))
          }

          VStack(alignment: .leading, spacing: 8) {
            HStack {
              Label("Confidence Threshold", systemImage: "slider.horizontal.3")
              Spacer()
              Text(viewModel.confidenceThresholdPercentage)
                .foregroundColor(.secondary)
            }

            Slider(
              value: Binding(
                get: { viewModel.confidenceThreshold },
                set: { newValue in
                  viewModel.confidenceThreshold = newValue
                  cameraViewModel.confidenceThreshold = newValue
                }
              ),
              in: 0.1...1.0,
              step: 0.05
            )
          }

          HStack {
            Label("Show Confidence Scores", systemImage: "percent")
            Spacer()
            Toggle(
              "",
              isOn: Binding(
                get: { viewModel.showConfidenceScores },
                set: { newValue in
                  viewModel.showConfidenceScores = newValue
                  cameraViewModel.showConfidenceScores = newValue
                }
              ))
          }

          VStack(alignment: .leading, spacing: 8) {
            HStack {
              Label("Processing Interval", systemImage: "timer")
              Spacer()
              Text("\(viewModel.frameProcessingInterval)")
                .foregroundColor(.secondary)
            }

            Text(viewModel.frameProcessingDescription)
              .font(.caption)
              .foregroundColor(.secondary)

            Stepper(
              "",
              value: $viewModel.frameProcessingInterval,
              in: 1...20,
              step: 1
            )
            .labelsHidden()
          }
        }

        // MARK: - Performance Settings Section
        Section("Performance") {
          VStack(alignment: .leading, spacing: 8) {
            HStack {
              Label("Max Detections", systemImage: "number.square")
              Spacer()
              Text("\(viewModel.maxDetectionsPerFrame)")
                .foregroundColor(.secondary)
            }

            Text(viewModel.maxDetectionsDescription)
              .font(.caption)
              .foregroundColor(.secondary)

            Stepper(
              "",
              value: $viewModel.maxDetectionsPerFrame,
              in: 1...50,
              step: 1
            )
            .labelsHidden()
          }
        }

        // MARK: - Storage Information Section
        Section("Storage") {
          HStack {
            Label("Available Storage", systemImage: "internaldrive")
            Spacer()
            Text(viewModel.availableStorage)
              .foregroundColor(.secondary)
          }

          HStack {
            Label("Model Storage Used", systemImage: "brain.head.profile")
            Spacer()
            Text(viewModel.usedModelStorage)
              .foregroundColor(.secondary)
          }

          Button(action: {
            showingClearModelsAlert = true
          }) {
            Label("Clear Downloaded Models", systemImage: "trash")
              .foregroundColor(.red)
          }
        }

        // MARK: - Reset Section
        Section {
          Button(action: {
            showingResetAlert = true
          }) {
            Label("Reset to Defaults", systemImage: "arrow.counterclockwise")
              .foregroundColor(.red)
          }
        }
      }
      .navigationTitle("Settings")
      .toolbar {
        ToolbarItem(placement: .navigationBarTrailing) {
          Button("Refresh") {
            viewModel.updateStorageInfo()
          }
        }
      }
      .alert("Reset Settings", isPresented: $showingResetAlert) {
        Button("Reset", role: .destructive) {
          viewModel.resetToDefaults()
        }
        Button("Cancel", role: .cancel) {}
      } message: {
        Text("This will reset all settings to their default values. This action cannot be undone.")
      }
      .alert("Clear Downloaded Models", isPresented: $showingClearModelsAlert) {
        Button("Clear", role: .destructive) {
          viewModel.clearAllDownloadedModels()
        }
        Button("Cancel", role: .cancel) {}
      } message: {
        Text(
          "This will delete all downloaded models. Bundled models will not be affected. This action cannot be undone."
        )
      }
    }
  }
}

// MARK: - Settings Row Views
struct SettingsRowView: View {
  let title: String
  let subtitle: String?
  let icon: String
  let value: String?

  init(title: String, subtitle: String? = nil, icon: String, value: String? = nil) {
    self.title = title
    self.subtitle = subtitle
    self.icon = icon
    self.value = value
  }

  var body: some View {
    HStack {
      Label(title, systemImage: icon)

      if let subtitle = subtitle {
        VStack(alignment: .leading) {
          Text(title)
          Text(subtitle)
            .font(.caption)
            .foregroundColor(.secondary)
        }
      }

      Spacer()

      if let value = value {
        Text(value)
          .foregroundColor(.secondary)
      }
    }
  }
}

struct StepperSettingView: View {
  let title: String
  let subtitle: String?
  let icon: String
  @Binding var value: Int
  let range: ClosedRange<Int>
  let step: Int

  var body: some View {
    VStack(alignment: .leading, spacing: 8) {
      HStack {
        Label(title, systemImage: icon)
        Spacer()
        Text("\(value)")
          .foregroundColor(.secondary)
      }

      if let subtitle = subtitle {
        Text(subtitle)
          .font(.caption)
          .foregroundColor(.secondary)
      }

      Stepper("", value: $value, in: range, step: step)
        .labelsHidden()
    }
  }
}

#Preview {
  SettingsView()
    .environmentObject(SettingsViewModel())
    .environmentObject(CameraViewModel())
}
